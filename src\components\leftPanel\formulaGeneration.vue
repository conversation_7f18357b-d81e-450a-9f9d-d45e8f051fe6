<!-- 配方生成 -->
<template>
  <CPanel>
    <template #header>配方生成</template>
    <template #content>
      <div class="formula-list">
        <div class="formula-item" v-for="(item, index) in formulaList" :key="index">
          <div class="formula-info">
            <div class="formula-name">{{ item.name }}</div>
            <div class="formula-time">{{ item.time }}</div>
          </div>
          <div class="formula-chart">
            <div class="chart-container" :id="`formula-chart-${index}`"></div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import CPanel from '@/components/common/CPanel.vue'

const formulaList = ref([
  { name: '母牛配方', time: '张三养殖场 | 2025-8-28 19:17:36' },
  { name: '母牛配方', time: '张三养殖场 | 2025-8-28 19:17:36' },
  { name: '母牛配方', time: '张三养殖场 | 2025-8-28 19:17:36' },
  { name: '母牛配方', time: '张三养殖场 | 2025-8-28 19:17:36' }
])

const initCharts = () => {
  formulaList.value.forEach((item, index) => {
    const chartDom = document.getElementById(`formula-chart-${index}`)
    if (chartDom) {
      const myChart = echarts.init(chartDom)
      const option = {
        series: [{
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '50%'],
          data: [
            { value: 35, name: '育肥牛', itemStyle: { color: '#00D4FF' } },
            { value: 25, name: '后备母牛', itemStyle: { color: '#FFB800' } },
            { value: 20, name: '妊娠母牛', itemStyle: { color: '#00FF88' } },
            { value: 20, name: '哺乳母牛', itemStyle: { color: '#FF6B6B' } }
          ],
          label: {
            show: false
          },
          labelLine: {
            show: false
          }
        }]
      }
      myChart.setOption(option)
    }
  })
}

onMounted(() => {
  nextTick(() => {
    initCharts()
  })
})
</script>

<style lang="scss" scoped>
.formula-list {
  height: 200px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-y: auto;

  .formula-item {
    display: flex;
    align-items: center;
    background: rgba(0, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 6px;
    padding: 8px 12px;

    .formula-info {
      flex: 1;

      .formula-name {
        font-size: 14px;
        color: #00FFFF;
        font-weight: bold;
        margin-bottom: 4px;
      }

      .formula-time {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.6);
      }
    }

    .formula-chart {
      width: 60px;
      height: 60px;

      .chart-container {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
