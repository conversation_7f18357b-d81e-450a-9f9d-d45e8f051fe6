<!-- 技术课堂 -->
<template>
  <CPanel>
    <template #header>技术课堂</template>
    <template #content>
      <div class="content">
        <div class="left-section">
          <div class="center-circle">
            <div class="total-number">256</div>
          </div>
          <div class="chart-container" id="technical-chart"></div>
        </div>
        <div class="right-section">
          <div class="stats-grid">
            <div class="stat-item" v-for="(item, index) in statsList" :key="index">
              <div class="stat-color" :style="{ backgroundColor: item.color }"></div>
              <span class="stat-name">{{ item.name }}</span>
              <span class="stat-percent">{{ item.percent }}%</span>
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import CPanel from '@/components/common/CPanel.vue'

const statsList = ref([
  { name: '主题1', percent: 34, color: '#E83F46' },
  { name: '主题5', percent: 34, color: '#0DC869' },
  { name: '主题2', percent: 30, color: '#FF8D39' },
  { name: '主题6', percent: 30, color: '#00D4DC' },
  { name: '主题3', percent: 34, color: '#FFD339' },
  { name: '主题7', percent: 34, color: '#4F7DFF' },
  { name: '主题4', percent: 30, color: '#B4E61A' },
  { name: '主题8', percent: 30, color: '#AF48FF' }
])

const initChart = () => {
  const chartDom = document.getElementById('technical-chart')
  if (chartDom) {
    const myChart = echarts.init(chartDom)
    const option = {
      series: [{
        type: 'pie',
        radius: ['40%', '75%'],
        center: ['50%', '50%'],
        data: statsList.value.map(item => ({
          value: item.percent,
          name: item.name,
          itemStyle: { color: item.color }
        })),
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
    myChart.setOption(option)
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style lang="scss" scoped>
.content {
  height: 180px;
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 0 15px;
}

.left-section {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 130px;

  .center-circle {
    position: absolute;
    z-index: 2;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;

    .total-number {
      font-size: 28px;
      font-weight: bold;
      color: #fff;
      line-height: 1;
    }
  }
}

.right-section {
  flex: 1;

  .stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px 25px;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .stat-color {
        width: 12px;
        height: 12px;
        border-radius: 2px;
        flex-shrink: 0;
      }

      .stat-name {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
        flex: 1;
      }

      .stat-percent {
        font-size: 14px;
        color: #00FFFF;
        font-weight: bold;
        min-width: 40px;
        text-align: right;
      }
    }
  }
}

.chart-container {
  width: 120px;
  height: 130px;
}
</style>
